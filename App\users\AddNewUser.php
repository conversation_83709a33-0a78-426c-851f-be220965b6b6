<?php
// زيادة وقت التنفيذ للصفحة
set_time_limit(300);

// Start session and verify session data
session_start();
if (session_status() === PHP_SESSION_NONE) {
    ini_set('session.cookie_httponly', 1);
    ini_set('session.cookie_secure', 1);
    ini_set('session.use_only_cookies', 1);
    ini_set('session.cookie_samesite', 'Strict');
}

// تضمين ملف قاعدة البيانات أولاً
include_once $_SERVER['DOCUMENT_ROOT'].'/config/Databasehost.php';

// تضمين نظام الصلاحيات
include_once $_SERVER['DOCUMENT_ROOT'].'/App/includes/permissions.php';

// Enhanced security logging
function logSecurityEvent($event_type, $details) {
    global $conn;
    try {
        // التحقق من وجود الاتصال بقاعدة البيانات
        if (!isset($conn) || $conn === null) {
            error_log("Database connection not available for security logging");
            return;
        }

        // التحقق من وجود جدول security_logs
        $check_table = $conn->query("SHOW TABLES LIKE 'security_logs'");
        if ($check_table->num_rows == 0) {
            // إنشاء الجدول إذا لم يكن موجوداً
            $create_table = "CREATE TABLE IF NOT EXISTS security_logs (
                id INT AUTO_INCREMENT PRIMARY KEY,
                event_type VARCHAR(100) NOT NULL,
                user_email VARCHAR(255),
                ip_address VARCHAR(45),
                event_data TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )";
            $conn->query($create_table);
        }

        $sql = "INSERT INTO security_logs (event_type, user_email, ip_address, event_data, created_at)
                VALUES (?, ?, ?, ?, NOW())";
        $stmt = $conn->prepare($sql);
        if ($stmt) {
            $ip = $_SERVER['REMOTE_ADDR'];
            $user = $_SESSION['username'] ?? 'unknown';
            $details_json = json_encode($details);
            $stmt->bind_param("ssss", $event_type, $user, $ip, $details_json);
            $stmt->execute();
            $stmt->close();
        }
    } catch (Exception $e) {
        error_log("Security logging failed: " . $e->getMessage());
    }
}

// Enhanced permission check
function checkAdminAccess() {
    if (!isset($_SESSION['access_level']) || $_SESSION['access_level'] !== 'Admin') {
        logSecurityEvent('unauthorized_access', [
            'user' => $_SESSION['username'] ?? 'unknown',
            'ip' => $_SERVER['REMOTE_ADDR'],
            'action' => 'add_user'
        ]);
        header("Location: ../Loginpage.php");
        exit();
    }
}

// Create CSRF token if not exists
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

// التحقق من صلاحية الوصول للصفحة
checkPagePermission('users', $_SESSION['access_level'] ?? 'Viewer');

// Check admin access
checkAdminAccess();

if (!isset($_SESSION['username']) || !isset($_SESSION['random_code'])) {
    header("Location: Loginpage.php");
    exit();
}

$email = $_SESSION['username'];
$eemail = $_SESSION['username'];

// استيراد ملفات PHPMailer
include $_SERVER['DOCUMENT_ROOT']. '/PHPMailer/vendor/phpmailer/phpmailer/src/PHPMailer.php';
include $_SERVER['DOCUMENT_ROOT']. '/PHPMailer/vendor/phpmailer/phpmailer/src/SMTP.php';
include $_SERVER['DOCUMENT_ROOT']. '/PHPMailer/vendor/phpmailer/phpmailer/src/Exception.php';

use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\Exception;

// دالة إرسال البريد الإلكتروني
function sendWelcomeEmail($email, $firstname, $password, $userDetails = []) {
    try {
        // زيادة وقت التنفيذ لإرسال البريد الإلكتروني
        set_time_limit(300);

        // استيراد إعدادات البريد الإلكتروني
        $mailConfig = include $_SERVER['DOCUMENT_ROOT'].'/config/Emailinformation.php';
        $mail = new PHPMailer(true);

        // إعدادات الخادم
        $mail->isSMTP();
        $mail->Host = $mailConfig['host'];
        $mail->SMTPAuth = true;
        $mail->Username = $mailConfig['username'];
        $mail->Password = $mailConfig['password'];
        $mail->SMTPSecure = $mailConfig['encryption']; // استخدام الإعداد من الملف
        $mail->Port = $mailConfig['port'];
        $mail->CharSet = 'UTF-8';

        // إعدادات إضافية لتحسين الاتصال
        $mail->Timeout = 30; // تقليل timeout إلى 30 ثانية
        $mail->SMTPKeepAlive = false; // تعطيل keep alive لتجنب مشاكل الاتصال
        $mail->SMTPDebug = 0; // تعطيل debug في الإنتاج (استخدم 2 للتشخيص)
        $mail->SMTPAutoTLS = false; // تعطيل auto TLS

        // إعدادات SSL محسنة
        $mail->SMTPOptions = array(
            'ssl' => array(
                'verify_peer' => false,
                'verify_peer_name' => false,
                'allow_self_signed' => true,
                'cafile' => false,
                'capath' => false,
                'ciphers' => 'DEFAULT:!DH'
            ),
            'tls' => array(
                'verify_peer' => false,
                'verify_peer_name' => false,
                'allow_self_signed' => true
            )
        );

        // إعدادات المرسل والمستلم
        $mail->setFrom($mailConfig['from_email'], $mailConfig['from_name']);
        $mail->addAddress($email, $firstname);

        // محتوى البريد الإلكتروني
        $mail->isHTML(true);
        $mail->Subject = 'Welcome to Kalam CX - Your Account Details';

        // إعداد البيانات
        $fullName = trim(($userDetails['firstname'] ?? $firstname) . ' ' . ($userDetails['lastname'] ?? ''));
        $teamLeader = $userDetails['leader'] ?? 'Not Assigned';
        $joinDate = $userDetails['joindate'] ?? 'Not Specified';
        $country = $userDetails['country'] ?? 'Not Specified';
        $phoneNumber = $userDetails['phonenumber'] ?? 'Not Specified';
        $seatNumber = $userDetails['seat'] ?? 'Not Assigned';
        $client = $userDetails['client'] ?? 'Not Specified';
        $language = $userDetails['language'] ?? 'Not Specified';
        $module = $userDetails['module'] ?? 'Not Specified';

        // تصميم البريد الإلكتروني
        $emailBody = "
        <html>
        <head>
            <style>
                body {
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    margin: 0;
                    padding: 0;
                    background-color: #f5f5f5;
                }
                .container {
                    max-width: 650px;
                    margin: 20px auto;
                    background-color: #ffffff;
                    border-radius: 10px;
                    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                    overflow: hidden;
                }
                .header {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    text-align: center;
                    padding: 30px 20px;
                }
                .header h1 {
                    margin: 0;
                    font-size: 28px;
                    font-weight: 300;
                }
                .content {
                    padding: 30px;
                }
                .welcome-text {
                    color: #333;
                    font-size: 16px;
                    line-height: 1.6;
                    margin-bottom: 25px;
                }
                .info-section {
                    background-color: #f8f9fa;
                    border-radius: 8px;
                    padding: 20px;
                    margin: 20px 0;
                }
                .info-title {
                    color: #495057;
                    font-size: 18px;
                    font-weight: 600;
                    margin-bottom: 15px;
                    border-bottom: 2px solid #dee2e6;
                    padding-bottom: 8px;
                }
                .info-grid {
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 15px;
                }
                .info-item {
                    display: flex;
                    flex-direction: column;
                }
                .info-label {
                    color: #6c757d;
                    font-size: 12px;
                    font-weight: 600;
                    text-transform: uppercase;
                    letter-spacing: 0.5px;
                    margin-bottom: 4px;
                }
                .info-value {
                    color: #212529;
                    font-size: 14px;
                    font-weight: 500;
                }
                .credentials {
                    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
                    color: white;
                    border-radius: 8px;
                    padding: 20px;
                    margin: 20px 0;
                }
                .credentials h3 {
                    margin: 0 0 15px 0;
                    font-size: 16px;
                }
                .cred-item {
                    background-color: rgba(255, 255, 255, 0.2);
                    border-radius: 5px;
                    padding: 10px;
                    margin: 8px 0;
                    font-family: 'Courier New', monospace;
                }
                .footer {
                    background-color: #f8f9fa;
                    text-align: center;
                    padding: 20px;
                    border-top: 1px solid #dee2e6;
                }
                .footer p {
                    color: #6c757d;
                    font-size: 12px;
                    margin: 5px 0;
                }
                @media (max-width: 600px) {
                    .info-grid { grid-template-columns: 1fr; }
                }
            </style>
        </head>
        <body>
            <div class='container'>
                <div class='header'>
                    <h1>Welcome to Kalam CX</h1>
                </div>

                <div class='content'>
                    <div class='welcome-text'>
                        <p>Dear <strong>{$fullName}</strong>,</p>
                        <p>Welcome to the Kalam CX team! Your account has been successfully created and you're now ready to get started.</p>
                    </div>

                    <div class='credentials'>
                        <h3>🔐 Login Credentials</h3>
                        <div class='cred-item'>
                            <strong>Email:</strong> {$email}
                        </div>
                        <div class='cred-item'>
                            <strong>Password:</strong> {$password}
                        </div>
                    </div>

                    <div class='info-section'>
                        <div class='info-title'>👤 Personal Information</div>
                        <div class='info-grid'>
                            <div class='info-item'>
                                <div class='info-label'>Full Name</div>
                                <div class='info-value'>{$fullName}</div>
                            </div>
                            <div class='info-item'>
                                <div class='info-label'>Email Address</div>
                                <div class='info-value'>{$email}</div>
                            </div>
                            <div class='info-item'>
                                <div class='info-label'>Country</div>
                                <div class='info-value'>{$country}</div>
                            </div>
                            <div class='info-item'>
                                <div class='info-label'>Phone Number</div>
                                <div class='info-value'>{$phoneNumber}</div>
                            </div>
                        </div>
                    </div>

                    <div class='info-section'>
                        <div class='info-title'>💼 Work Information</div>
                        <div class='info-grid'>
                            <div class='info-item'>
                                <div class='info-label'>Team Leader</div>
                                <div class='info-value'>{$teamLeader}</div>
                            </div>
                            <div class='info-item'>
                                <div class='info-label'>Join Date</div>
                                <div class='info-value'>{$joinDate}</div>
                            </div>
                            <div class='info-item'>
                                <div class='info-label'>Seat Number</div>
                                <div class='info-value'>{$seatNumber}</div>
                            </div>
                            <div class='info-item'>
                                <div class='info-label'>Client</div>
                                <div class='info-value'>{$client}</div>
                            </div>
                            <div class='info-item'>
                                <div class='info-label'>Language</div>
                                <div class='info-value'>{$language}</div>
                            </div>
                            <div class='info-item'>
                                <div class='info-label'>Module</div>
                                <div class='info-value'>{$module}</div>
                            </div>
                        </div>
                    </div>

                    <div style='background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 15px; margin: 20px 0;'>
                        <p style='color: #856404; margin: 0; font-size: 14px;'>
                            <strong>🔒 Security Notice:</strong> For your security, please change your password after your first login.
                        </p>
                    </div>
                </div>

                <div class='footer'>
                    <p><strong>Kalam CX System</strong></p>
                    <p>This is an automated message, please do not reply to this email.</p>
                    <p>If you did not request this account, please contact the administrator immediately.</p>
                </div>
            </div>
        </body>
        </html>";

        $mail->Body = $emailBody;
        $mail->AltBody = "Welcome to Kalam CX!\n\nDear {$fullName},\n\nYour account has been successfully created.\n\nLOGIN CREDENTIALS:\nEmail: {$email}\nPassword: {$password}\n\nPERSONAL INFORMATION:\nName: {$fullName}\nCountry: {$country}\nPhone: {$phoneNumber}\n\nWORK INFORMATION:\nTeam Leader: {$teamLeader}\nJoin Date: {$joinDate}\nSeat Number: {$seatNumber}\nClient: {$client}\nLanguage: {$language}\nModule: {$module}\n\nFor security reasons, please change your password after your first login.\n\nThis is an automated message, please do not reply to this email.";

        // إرسال البريد الإلكتروني مع retry mechanism
        $max_attempts = 3;
        $attempt = 1;
        $sent = false;

        while ($attempt <= $max_attempts && !$sent) {
            try {
                error_log("Attempting to send email (attempt $attempt/$max_attempts) to: $email");
                $mail->send();
                $sent = true;
                error_log("Welcome email sent successfully to: $email on attempt $attempt");
            } catch (Exception $retry_e) {
                error_log("Email send attempt $attempt failed: " . $retry_e->getMessage());
                if ($attempt < $max_attempts) {
                    sleep(2); // انتظار ثانيتين قبل المحاولة التالية
                    $attempt++;
                } else {
                    throw $retry_e; // رمي الخطأ إذا فشلت جميع المحاولات
                }
            }
        }

        return true;
    } catch (Exception $e) {
        $error_message = "Failed to send welcome email to $email after $max_attempts attempts: " . $e->getMessage();
        error_log($error_message);

        // تسجيل تفاصيل إضافية للتشخيص
        error_log("Email configuration - Host: " . $mailConfig['host'] . ", Port: " . $mailConfig['port']);
        error_log("SMTP Error details: " . $mail->ErrorInfo);

        // حفظ بيانات البريد الإلكتروني في ملف للمحاولة لاحقاً
        $failed_email_data = [
            'email' => $email,
            'firstname' => $firstname,
            'password' => $password,
            'userDetails' => $userDetails,
            'timestamp' => date('Y-m-d H:i:s'),
            'error' => $e->getMessage()
        ];

        $failed_emails_file = $_SERVER['DOCUMENT_ROOT'] . '/logs/failed_emails.json';
        $failed_emails_dir = dirname($failed_emails_file);

        if (!is_dir($failed_emails_dir)) {
            mkdir($failed_emails_dir, 0755, true);
        }

        $existing_failed = [];
        if (file_exists($failed_emails_file)) {
            $existing_failed = json_decode(file_get_contents($failed_emails_file), true) ?: [];
        }

        $existing_failed[] = $failed_email_data;
        file_put_contents($failed_emails_file, json_encode($existing_failed, JSON_PRETTY_PRINT));

        return false;
    }
}

// Function to generate random code
function generateRandomCode($length = 8) {
    $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $code = '';
    for ($i = 0; $i < $length; $i++) {
        $code .= $characters[random_int(0, strlen($characters) - 1)];
    }
    return $code;
}

// دالة للتحقق من صحة البريد الإلكتروني
function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) &&
           preg_match('/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/', $email);
}

// دالة للتحقق من صحة رقم الهاتف
function validatePhone($phone) {
    return preg_match('/^[0-9+\-\s()]{8,20}$/', $phone);
}

// Process form data upon submission
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    try {
        // Verify CSRF token
        if (!isset($_POST['csrf_token']) || !hash_equals($_SESSION['csrf_token'], $_POST['csrf_token'])) {
            throw new Exception("Invalid security token");
        }

        // Sanitize and validate inputs
        $email = filter_input(INPUT_POST, 'email', FILTER_SANITIZE_EMAIL);
        $password = $_POST['password'];
        $firstname = filter_input(INPUT_POST, 'firstname', FILTER_SANITIZE_STRING);
        $lastname = filter_input(INPUT_POST, 'lastname', FILTER_SANITIZE_STRING);
        $country = filter_input(INPUT_POST, 'country', FILTER_SANITIZE_STRING);
        $leader = filter_input(INPUT_POST, 'leader', FILTER_SANITIZE_STRING);
        $access = filter_input(INPUT_POST, 'access', FILTER_SANITIZE_STRING);
        $phonenumber = filter_input(INPUT_POST, 'phonenumber', FILTER_SANITIZE_STRING);
        $joindate = filter_input(INPUT_POST, 'joindate', FILTER_SANITIZE_STRING);
        $seat = filter_input(INPUT_POST, 'seat', FILTER_SANITIZE_STRING);
        $client = filter_input(INPUT_POST, 'client', FILTER_SANITIZE_STRING);
        $language = filter_input(INPUT_POST, 'language', FILTER_SANITIZE_STRING);
        $module = filter_input(INPUT_POST, 'module', FILTER_SANITIZE_STRING);

        $errors = [];

        // Validate email
        if (!validateEmail($email)) {
            throw new Exception("Invalid email format");
        }

        // Enhanced password validation
        $password_errors = validatePassword($password);
        if (!empty($password_errors)) {
            $errors = array_merge($errors, $password_errors);
        }

        // Validate access level
        $allowed_access_levels = ['Super Admin', 'Admin', 'Editor', 'Manager', 'Leader', 'Viewer', 'Member'];
        if (!in_array($access, $allowed_access_levels)) {
            $errors[] = "Invalid access level";
        }

        if (empty($errors)) {
            $stmt = null;
            try {
                // التحقق من وجود البريد الإلكتروني
                $checkEmailQuery = "SELECT * FROM usersprofile WHERE Email = ?";
                $stmt = $conn->prepare($checkEmailQuery);
                $stmt->bind_param("s", $email);
                $stmt->execute();
                $emailResult = $stmt->get_result();

                if ($emailResult->num_rows > 0) {
                    $errors[] = "Email already exists";
                } else {
                    // إنشاء رمز فريد
                    do {
                        $code = bin2hex(random_bytes(4));
                        if ($stmt) {
                            $stmt->close();
                        }
                        $checkCodeQuery = "SELECT * FROM usersprofile WHERE Code = ?";
                        $stmt = $conn->prepare($checkCodeQuery);
                        $stmt->bind_param("s", $code);
                        $stmt->execute();
                        $codeResult = $stmt->get_result();
                    } while ($codeResult->num_rows > 0);

                    // تشفير كلمة المرور
                    $hashed_password = password_hash($password, PASSWORD_BCRYPT, ['cost' => 12]);

                    // بدء المعاملة
                    $conn->begin_transaction();

                    if ($stmt) {
                        $stmt->close();
                    }

                    $sql = "INSERT INTO usersprofile (Email, password, Firstname, Lastname, Country, leader, Access,
                            phonenumber, Joindate, Code, whoscreateuser, Seat, Client, Language, Module)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
                    $stmt = $conn->prepare($sql);
                    $stmt->bind_param("sssssssssssssss",
                        $email, $hashed_password, $firstname, $lastname, $country,
                        $leader, $access, $phonenumber, $joindate, $code,
                        $_SESSION['username'], $seat, $client, $language, $module);

                    if ($stmt->execute()) {
                        // تسجيل الحدث
                        logSecurityEvent('user_created', [
                            'new_user_email' => $email,
                            'access_level' => $access,
                            'created_by' => $_SESSION['username']
                        ]);

                        // إرسال البريد الإلكتروني مع جميع بيانات المستخدم
                        $userDetails = [
                            'firstname' => $firstname,
                            'lastname' => $lastname,
                            'leader' => $leader,
                            'joindate' => $joindate,
                            'country' => $country,
                            'phonenumber' => $phonenumber,
                            'seat' => $seat,
                            'client' => $client,
                            'language' => $language,
                            'module' => $module
                        ];

                        // محاولة إرسال البريد الإلكتروني مع timeout محدود
                        $email_sent = false;
                        try {
                            // تشغيل إرسال البريد في background process لتجنب timeout
                            $email_sent = sendWelcomeEmail($email, $firstname, $password, $userDetails);
                        } catch (Exception $email_error) {
                            error_log("Email sending failed during user creation: " . $email_error->getMessage());
                            $email_sent = false;
                        }

                        // تسجيل نتيجة إرسال البريد الإلكتروني
                        if ($email_sent) {
                            logSecurityEvent('welcome_email_sent', [
                                'recipient' => $email,
                                'status' => 'success'
                            ]);
                        } else {
                            logSecurityEvent('welcome_email_failed', [
                                'recipient' => $email,
                                'status' => 'failed'
                            ]);
                        }

                        $conn->commit();

                        // إظهار رسالة نجاح مع حالة البريد الإلكتروني
                        if ($email_sent) {
                            $success_message = "✅ User created successfully! Welcome email sent to $email.";
                            echo "<div class='alert alert-success'>$success_message</div>";
                        } else {
                            $success_message = "✅ User created successfully!";
                            $warning_message = "⚠️ However, welcome email could not be sent due to server timeout. The user account is active and login credentials are: <br><strong>Email:</strong> $email<br><strong>Password:</strong> $password";
                            echo "<div class='alert alert-success'>$success_message</div>";
                            echo "<div class='alert alert-warning'>$warning_message</div>";
                        }

                        // إعادة تعيين النموذج بعد النجاح
                        echo "<script>
                            setTimeout(function() {
                                document.getElementById('addUserForm').reset();
                            }, 3000);
                        </script>";
                        // header("Location: users.php?success=1&email_sent=" . ($email_sent ? "1" : "0"));
                        // exit();
                    } else {
                        throw new Exception("Error creating user: " . $stmt->error);
                    }
                }
            } catch (Exception $e) {
                if ($stmt) {
                    $stmt->close();
                }
                $conn->rollback();
                throw $e;
            }
        }
    } catch (Exception $e) {
        logSecurityEvent('user_creation_failed', [
            'error' => $e->getMessage(),
            'attempted_email' => $email ?? 'unknown'
        ]);
        $errors[] = "Error: " . $e->getMessage();
    }

    if (!empty($errors)) {
        echo "<div class='alert alert-danger'>";
        echo "<h4>Errors Found:</h4>";
        echo "<ul>";
        foreach ($errors as $error) {
            echo "<li>" . htmlspecialchars($error) . "</li>";
        }
        echo "</ul>";
        echo "</div>";
    }
}

// Enhanced password validation function
function validatePassword($password) {
    $errors = [];

    // Length check
    if (strlen($password) < 8) {
        $errors[] = "Password must be at least 8 characters long";
    }

    // Character type checks
    if (!preg_match('/[A-Z]/', $password)) {
        $errors[] = "Password must contain at least one uppercase letter";
    }
    if (!preg_match('/[a-z]/', $password)) {
        $errors[] = "Password must contain at least one lowercase letter";
    }
    if (!preg_match('/[0-9]/', $password)) {
        $errors[] = "Password must contain at least one number";
    }
    if (!preg_match('/[!@#$%^&*]/', $password)) {
        $errors[] = "Password must contain at least one special character (!@#$%^&*)";
    }

    // Common password check
    $common_passwords = ['password123', 'admin123', 'welcome123']; // Add more common passwords
    if (in_array(strtolower($password), $common_passwords)) {
        $errors[] = "Password is too common. Please choose a stronger password";
    }

    // Sequential characters check
    if (preg_match('/(.)\1{2,}/', $password)) {
        $errors[] = "Password contains too many repeated characters";
    }

    return $errors;
}

$conn->close();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add New User</title>
    <!-- Bootstrap 5 -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- jQuery -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
    <link rel="stylesheet" href="../assets/css/Add-edit-user.css">
</head>
<body class="add-user-page">
    <div class="container add-user-container">
        <div class="add-user-card">
            <div class="add-user-header">
                <div class="add-user-login-info">
                    <i class="fas fa-user-circle me-2"></i>Logged in as: <strong><?php echo htmlspecialchars($email); ?></strong>
                </div>
                <h2 class="add-user-title"><i class="fas fa-user-plus me-2"></i>Add New User</h2>
            </div>

            <form id="addUserForm" action="AddNewUser.php" method="post" class="add-user-form-grid">
                <input type="hidden" name="csrf_token" value="<?php echo htmlspecialchars($_SESSION['csrf_token']); ?>">
                <h4 class="add-user-section-title"><i class="fas fa-id-card me-2"></i>Basic Information</h4>

                <div class="mb-3">
                    <label for="email" class="add-user-form-label"><i class="fas fa-envelope me-2"></i>Email Address</label>
                    <input type="email" class="form-control add-user-form-control" id="email" name="email" required>
                </div>

                <div class="mb-3 password-field-wrapper">
                    <label for="password" class="add-user-form-label">
                        <i class="fas fa-lock me-2"></i>Password
                        <span class="password-strength-indicator"></span>
                    </label>
                    <div class="password-field-container">
                        <input type="password" class="form-control add-user-form-control password-field"
                               id="password" name="password" required
                               pattern="(?=.*\d)(?=.*[a-z])(?=.*[A-Z])(?=.*[!@#$%^&*]).{8,}"
                               title="Password must be at least 8 characters long and contain at least one uppercase letter, one lowercase letter, one number, and one special character">
                        <button type="button" class="password-toggle" onclick="togglePasswordVisibility(this)">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button type="button" class="password-generate" onclick="generateStrongPassword()" title="Generate Strong Password">
                            <i class="fas fa-key"></i>
                        </button>
                    </div>
                    <div class="password-strength-meter">
                        <div class="strength-meter-bar"></div>
                    </div>

                    <!-- Password Requirements Container -->
                    <div class="password-requirements-container" style="display: none;">
                        <div class="password-requirements">
                            <h6>Password Requirements:</h6>
                            <div id="length"><i class="fas fa-circle"></i> At least 8 characters</div>
                            <div id="uppercase"><i class="fas fa-circle"></i> One uppercase letter</div>
                            <div id="lowercase"><i class="fas fa-circle"></i> One lowercase letter</div>
                            <div id="number"><i class="fas fa-circle"></i> One number</div>
                            <div id="special"><i class="fas fa-circle"></i> One special character (!@#$%^&*)</div>
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <label for="firstname" class="add-user-form-label"><i class="fas fa-user me-2"></i>First Name</label>
                    <input type="text" class="form-control add-user-form-control" id="firstname" name="firstname" required>
                </div>

                <div class="mb-3">
                    <label for="lastname" class="add-user-form-label"><i class="fas fa-user me-2"></i>Last Name</label>
                    <input type="text" class="form-control add-user-form-control" id="lastname" name="lastname" required>
                </div>

                <h4 class="add-user-section-title"><i class="fas fa-user-shield me-2"></i>Access Information</h4>

                <div class="mb-3">
                    <label for="access" class="add-user-form-label"><i class="fas fa-key me-2"></i>Access Level</label>
                    <select class="form-select add-user-form-control" id="access" name="access">
                        <option value="Super Admin">Super Admin - Full system access</option>
                        <option value="Admin">Admin - User and database management</option>
                        <option value="Editor">Editor - Content editing and reports</option>
                        <option value="Manager">Manager - Team management</option>
                        <option value="Leader">Leader - Team view with limited edit</option>
                        <option value="Viewer">Viewer - Read-only access</option>
                        <option value="Member">Member - Agent access</option>
                    </select>
                </div>

                <div class="mb-3">
                    <label for="leader" class="add-user-form-label"><i class="fas fa-user-tie me-2"></i>Team Leader</label>
                    <input type="text" class="form-control add-user-form-control" id="leader" name="leader">
                </div>

                <div class="mb-3">
                    <label for="joindate" class="add-user-form-label"><i class="fas fa-calendar-alt me-2"></i>Join Date</label>
                    <input type="date" class="form-control add-user-form-control" id="joindate" name="joindate">
                </div>

                <h4 class="add-user-section-title"><i class="fas fa-info-circle me-2"></i>Additional Information</h4>

                <div class="mb-3">
                    <label for="country" class="add-user-form-label"><i class="fas fa-globe me-2"></i>Country</label>
                    <input type="text" class="form-control add-user-form-control" id="country" name="country">
                </div>

                <div class="mb-3">
                    <label for="phonenumber" class="add-user-form-label"><i class="fas fa-phone me-2"></i>Phone Number</label>
                    <input type="text" class="form-control add-user-form-control" id="phonenumber" name="phonenumber">
                </div>

                <div class="mb-3">
                    <label for="seat" class="add-user-form-label"><i class="fas fa-chair me-2"></i>Seat Number</label>
                    <input type="text" class="form-control add-user-form-control" id="seat" name="seat">
                </div>

                <div class="mb-3">
                    <label for="client" class="add-user-form-label"><i class="fas fa-briefcase me-2"></i>Client</label>
                    <input type="text" class="form-control add-user-form-control" id="client" name="client">
                </div>

                <div class="mb-3">
                    <label for="language" class="add-user-form-label"><i class="fas fa-language me-2"></i>Language</label>
                    <input type="text" class="form-control add-user-form-control" id="language" name="language">
                </div>

                <div class="mb-3">
                    <label for="module" class="add-user-form-label"><i class="fas fa-cubes me-2"></i>Module</label>
                    <input type="text" class="form-control add-user-form-control" id="module" name="module">
                </div>

                <button type="submit" class="btn add-user-btn">
                    <i class="fas fa-user-plus me-2"></i>Add New User
                </button>
            </form>
        </div>
    </div>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // دالة تبديل رؤية كلمة المرور
        function togglePasswordVisibility(button) {
            const input = button.previousElementSibling;
            const icon = button.querySelector('i');

            if (input.type === 'password') {
                input.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                input.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        }

        // Password strength calculation
        function calculatePasswordStrength(password) {
            let strength = 0;

            // Length check
            if (password.length >= 8) strength += 1;
            if (password.length >= 12) strength += 1;

            // Character type checks
            if (/[A-Z]/.test(password)) strength += 1;
            if (/[a-z]/.test(password)) strength += 1;
            if (/[0-9]/.test(password)) strength += 1;
            if (/[^A-Za-z0-9]/.test(password)) strength += 1;

            // Additional complexity
            if (password.length >= 16) strength += 1;
            if (/[A-Z]/.test(password) && /[a-z]/.test(password) && /[0-9]/.test(password) && /[^A-Za-z0-9]/.test(password)) {
                strength += 1;
            }

            return strength;
        }

        // Update password validation and strength feedback
        document.querySelector('input[name="password"]').addEventListener('input', function(e) {
            const password = e.target.value;
            const requirements = {
                length: password.length >= 8,
                uppercase: /[A-Z]/.test(password),
                lowercase: /[a-z]/.test(password),
                number: /[0-9]/.test(password),
                special: /[!@#$%^&*]/.test(password)
            };

            // Calculate password strength
            const strength = calculatePasswordStrength(password);
            const strengthBar = document.querySelector('.strength-meter-bar');
            const strengthIndicator = document.querySelector('.password-strength-indicator');

            // Update strength meter
            strengthBar.className = 'strength-meter-bar';
            if (password.length === 0) {
                strengthIndicator.textContent = '';
            } else if (strength <= 2) {
                strengthBar.classList.add('very-weak');
                strengthIndicator.textContent = 'Very Weak';
                strengthIndicator.style.color = '#dc3545';
            } else if (strength <= 4) {
                strengthBar.classList.add('weak');
                strengthIndicator.textContent = 'Weak';
                strengthIndicator.style.color = '#ffc107';
            } else if (strength <= 6) {
                strengthBar.classList.add('medium');
                strengthIndicator.textContent = 'Medium';
                strengthIndicator.style.color = '#fd7e14';
            } else if (strength <= 8) {
                strengthBar.classList.add('strong');
                strengthIndicator.textContent = 'Strong';
                strengthIndicator.style.color = '#28a745';
            } else {
                strengthBar.classList.add('very-strong');
                strengthIndicator.textContent = 'Very Strong';
                strengthIndicator.style.color = '#20c997';
            }

            // Show/hide requirements container based on password strength (if exists)
            const container = document.querySelector('.password-requirements-container');
            const allValid = Object.values(requirements).every(Boolean);

            if (container) {
                // Show requirements if password is weak or medium
                if (password.length > 0 && (strength <= 4 || !allValid)) {
                    container.style.display = 'block';
                    setTimeout(() => {
                        container.style.opacity = '1';
                        container.style.transform = 'translateY(-50%) translateX(0)';
                    }, 10);
                } else {
                    container.style.opacity = '0';
                    container.style.transform = 'translateY(-50%) translateX(20px)';
                    setTimeout(() => {
                        if (container.style.opacity === '0') {
                            container.style.display = 'none';
                        }
                    }, 300);
                }
            }

            // Update visual indicators (if they exist)
            Object.keys(requirements).forEach(req => {
                const element = document.getElementById(req);
                if (element) {
                    if (requirements[req]) {
                        element.classList.add('valid');
                        element.style.color = '#28a745';
                        const icon = element.querySelector('i');
                        if (icon) icon.style.color = '#28a745';
                    } else {
                        element.classList.remove('valid');
                        element.style.color = '#6c757d';
                        const icon = element.querySelector('i');
                        if (icon) icon.style.color = '#adb5bd';
                    }
                }
            });

            // Update input field styling
            this.classList.toggle('error', !allValid && password.length > 0);
        });

        // Form validation before submission
        document.getElementById('addUserForm').addEventListener('submit', function(e) {
            const password = document.querySelector('input[name="password"]').value;
            const requirements = {
                length: password.length >= 8,
                uppercase: /[A-Z]/.test(password),
                lowercase: /[a-z]/.test(password),
                number: /[0-9]/.test(password),
                special: /[!@#$%^&*]/.test(password)
            };

            if (!Object.values(requirements).every(Boolean)) {
                e.preventDefault();
                alert('Please ensure the password meets all requirements');
                return false;
            }
        });

        // Generate strong password function
        function generateStrongPassword() {
            const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
            const lowercase = 'abcdefghijklmnopqrstuvwxyz';
            const numbers = '0123456789';
            const special = '!@#$%^&*';

            // Ensure at least one of each character type
            let password = '';
            password += uppercase[Math.floor(Math.random() * uppercase.length)];
            password += lowercase[Math.floor(Math.random() * lowercase.length)];
            password += numbers[Math.floor(Math.random() * numbers.length)];
            password += special[Math.floor(Math.random() * special.length)];

            // Add more random characters to reach 12 characters
            const allChars = uppercase + lowercase + numbers + special;
            for (let i = password.length; i < 12; i++) {
                password += allChars[Math.floor(Math.random() * allChars.length)];
            }

            // Shuffle the password
            password = password.split('').sort(() => Math.random() - 0.5).join('');

            // Set the password and trigger validation
            const passwordInput = document.querySelector('input[name="password"]');
            passwordInput.value = password;
            passwordInput.dispatchEvent(new Event('input'));

            // Show password briefly
            passwordInput.type = 'text';
            setTimeout(() => {
                passwordInput.type = 'password';
            }, 1000);
        }
    </script>
</body>
</html>
<link rel="stylesheet" href="../assets/css/Add-edit-user.css">
<style>
/* Update Container Size */
.add-user-container {
    max-width: 800px;
    margin: 1.5rem auto;
    padding: 0 1rem;
}

.add-user-card {
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
}

.add-user-form-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
}

/* Password Field Styling */
.password-field-wrapper {
    grid-column: 1 / -1;
    position: relative;
}

.password-field-container {
    position: relative;
    display: flex;
    align-items: center;
}

.password-field {
    padding-right: 5rem;
}

.password-toggle,
.password-generate {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    padding: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    border-radius: 4px;
    width: 32px;
    height: 32px;
}

.password-toggle {
    right: 40px;
}

.password-generate {
    right: 5px;
    background-color: #f8f9fa;
}

.password-toggle:hover,
.password-generate:hover {
    color: #495057;
    background-color: #e9ecef;
}

.password-generate:hover {
    background-color: #e9ecef;
    transform: translateY(-50%) scale(1.05);
}

.password-toggle i,
.password-generate i {
    font-size: 0.9rem;
}

/* Password Strength Meter */
.password-strength-meter {
    margin-top: 5px;
    height: 4px;
    background-color: #e9ecef;
    border-radius: 2px;
    overflow: hidden;
}

.strength-meter-bar {
    height: 100%;
    width: 0%;
    transition: all 0.3s ease;
    border-radius: 2px;
}

.strength-meter-bar.very-weak {
    width: 20%;
    background-color: #dc3545;
}

.strength-meter-bar.weak {
    width: 40%;
    background-color: #ffc107;
}

.strength-meter-bar.medium {
    width: 60%;
    background-color: #fd7e14;
}

.strength-meter-bar.strong {
    width: 80%;
    background-color: #28a745;
}

.strength-meter-bar.very-strong {
    width: 100%;
    background-color: #20c997;
}

/* Password Requirements Container */
.password-requirements-container {
    position: absolute;
    right: 0;
    top: 100%;
    width: 280px;
    z-index: 1000;
    opacity: 0;
    transition: all 0.3s ease;
    pointer-events: none;
    margin-top: 5px;
}

.password-requirements {
    background: #ffffff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    padding: 1rem;
    border: 1px solid #e9ecef;
    font-size: 0.8rem;
}

.password-requirements h6 {
    margin: 0 0 10px 0;
    color: #495057;
    font-size: 0.9rem;
}

.password-requirements div {
    margin: 5px 0;
    color: #6c757d;
    transition: color 0.3s ease;
}

.password-requirements div i {
    margin-right: 8px;
    font-size: 0.7rem;
    transition: color 0.3s ease;
}

.password-requirements div.valid {
    color: #28a745;
}

.password-requirements div.valid i {
    color: #28a745;
}

/* Responsive Design */
@media (max-width: 992px) {
    .add-user-container {
        max-width: 600px;
    }
}

@media (max-width: 768px) {
    .add-user-container {
        margin: 1rem auto;
    }

    .add-user-form-grid {
        grid-template-columns: 1fr;
    }

    .password-requirements-container {
        position: static;
        transform: none;
        width: 100%;
        max-width: 250px;
        margin: 0.75rem auto;
    }
}

@media (max-width: 480px) {
    .add-user-container {
        padding: 0 0.75rem;
    }

    .add-user-card {
        padding: 1rem;
    }
}
</style>